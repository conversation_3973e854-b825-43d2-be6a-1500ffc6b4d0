"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApplication: () => (/* binding */ createApplication),\n/* harmony export */   deleteApplication: () => (/* binding */ deleteApplication),\n/* harmony export */   getApplications: () => (/* binding */ getApplications),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   requestNotification: () => (/* binding */ requestNotification),\n/* harmony export */   updateApplication: () => (/* binding */ updateApplication)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Use the Next.js API proxy to avoid CORS issues\nconst API_BASE_URL = \"/api/proxy\";\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\nconst getApplications = async ()=>{\n    console.log(\"[getApplications] Sending GET request to /apps\");\n    try {\n        const response = await api.get(\"/apps\");\n        console.log(\"[getApplications] Response received:\", response.status, response.data);\n        return response.data;\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"[getApplications] Error occurred:\", {\n            message: error.message,\n            status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n            data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n        });\n        throw new Error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to fetch applications\");\n    }\n};\nconst createApplication = async (applicationData)=>{\n    console.debug(\"[createApplication] Input data:\", applicationData);\n    try {\n        const response = await api.post(\"/app\", applicationData);\n        console.debug(\"[createApplication] Response status:\", response.status);\n        console.debug(\"[createApplication] Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        var _error_response, _error_response_data, _error_response1;\n        console.error(\"[createApplication] Error:\", error);\n        console.error(\"[createApplication] Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n        throw new Error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to create application -api\");\n    }\n};\nconst requestNotification = async (applicationId)=>{};\nconst updateApplication = async (id, applicationData)=>{\n    try {\n        const response = await api.put(\"/app/\".concat(id), applicationData);\n        return response.data;\n    } catch (error) {\n        var _error_response;\n        console.error(\"[updateApplication] Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n        throw new Error(\"Failed to update application\");\n    }\n};\nconst deleteApplication = async (id)=>{\n    try {\n        await api.delete(\"/app/\".concat(id));\n    } catch (error) {\n        throw new Error(\"Failed to delete application\");\n    }\n};\nconst getNotifications = async (applicationId)=>{\n    try {\n        const response = await api.get(\"/app/\".concat(applicationId, \"/notifications\"));\n        return response.data;\n    } catch (error) {\n        throw new Error(\"Failed to fetch notifications\");\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ })

});