globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\":[],"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\page":[],"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(rsc)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}