export interface Application {
  Application: string
  App_name: string
  Email: string
  Domain: string
  'SES-Domain-ARN'?: string
  'SNS-Topic-ARN'?: string
}

export interface ApplicationFormData {
  App_name: string
  Application: string
  Email: string
  Domain: string
}

export interface Notification {
  id: string
  type: 'EMAIL' | 'SMS' | 'PUSH'
  recipient: string
  subject?: string
  message: string
  status: 'sent' | 'pending' | 'failed'
  timestamp: string
}

export interface User {
  username: string
}

export interface ApplicationListProps {
  applications: Application[];
  onUpdate: () => void;
}