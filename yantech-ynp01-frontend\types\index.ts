export interface Application {
  Application: string
  App_name: string
  Email: string
  Domain: string
  'SES-Domain-ARN'?: string
  'SNS-Topic-ARN'?: string
}

export interface ApplicationFormData {
  App_name: string
  Application: string
  Email: string
  Domain: string
}

export interface Notification {
  Application: string;
  Recipient: string;
  Subject: string;
  Message: string;
  OutputType: string;
  Interval: object;
}

export interface User {
  username: string
}

export interface ApplicationListProps {
  applications: Application[];
  onUpdate: () => void;
}