/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2f40ff70c19f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXENsb3VkIEhlcm9lcyBBZnJpY2FcXFlBTlRFQ0gtWU5QMDEtIEZyb250RW5kXFx5YW50ZWNoLXlucDAxLWZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmY0MGZmNzBjMTlmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Admin Dashboard - Notification System',\n    description: 'Admin dashboard for managing notification applications and AWS resources'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFIZ0I7QUFDK0I7QUFJOUMsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7c0JBQzlCLDRFQUFDQywrREFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXENsb3VkIEhlcm9lcyBBZnJpY2FcXFlBTlRFQ0gtWU5QMDEtIEZyb250RW5kXFx5YW50ZWNoLXlucDAxLWZyb250ZW5kXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQWRtaW4gRGFzaGJvYXJkIC0gTm90aWZpY2F0aW9uIFN5c3RlbScsXG4gIGRlc2NyaXB0aW9uOiAnQWRtaW4gZGFzaGJvYXJkIGZvciBtYW5hZ2luZyBub3RpZmljYXRpb24gYXBwbGljYXRpb25zIGFuZCBBV1MgcmVzb3VyY2VzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Cloud Heroes Africa\\YANTECH-YNP01- FrontEnd\\yantech-ynp01-frontend\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDQ2xvdWQlMjBIZXJvZXMlMjBBZnJpY2ElNUMlNUNZQU5URUNILVlOUDAxLSUyMEZyb250RW5kJTVDJTVDeWFudGVjaC15bnAwMS1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBMEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGJpY2hlXFxcXENsb3VkIEhlcm9lcyBBZnJpY2FcXFxcWUFOVEVDSC1ZTlAwMS0gRnJvbnRFbmRcXFxceWFudGVjaC15bnAwMS1mcm9udGVuZFxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(rsc)/./contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDQ2xvdWQlMjBIZXJvZXMlMjBBZnJpY2ElNUMlNUNZQU5URUNILVlOUDAxLSUyMEZyb250RW5kJTVDJTVDeWFudGVjaC15bnAwMS1mcm9udGVuZCU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDQ2xvdWQlMjBIZXJvZXMlMjBBZnJpY2ElNUMlNUNZQU5URUNILVlOUDAxLSUyMEZyb250RW5kJTVDJTVDeWFudGVjaC15bnAwMS1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNiaWNoZSU1QyU1Q0Nsb3VkJTIwSGVyb2VzJTIwQWZyaWNhJTVDJTVDWUFOVEVDSC1ZTlAwMS0lMjBGcm9udEVuZCU1QyU1Q3lhbnRlY2gteW5wMDEtZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxDbG91ZCBIZXJvZXMgQWZyaWNhXFxcXFlBTlRFQ0gtWU5QMDEtIEZyb250RW5kXFxcXHlhbnRlY2gteW5wMDEtZnJvbnRlbmRcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction HomePage() {\n    const { isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (isAuthenticated) {\n                router.push('/dashboard');\n            } else {\n                router.push('/login');\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        isAuthenticated,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\app\\\\page.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFaUM7QUFDVTtBQUNLO0FBRWpDLFNBQVNHO0lBQ3RCLE1BQU0sRUFBRUMsZUFBZSxFQUFFLEdBQUdGLDhEQUFPQTtJQUNuQyxNQUFNRyxTQUFTSiwwREFBU0E7SUFFeEJELGdEQUFTQTs4QkFBQztZQUNSLElBQUlJLGlCQUFpQjtnQkFDbkJDLE9BQU9DLElBQUksQ0FBQztZQUNkLE9BQU87Z0JBQ0xELE9BQU9DLElBQUksQ0FBQztZQUNkO1FBQ0Y7NkJBQUc7UUFBQ0Y7UUFBaUJDO0tBQU87SUFFNUIscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7Ozs7Ozs7Ozs7O0FBR3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxDbG91ZCBIZXJvZXMgQWZyaWNhXFxZQU5URUNILVlOUDAxLSBGcm9udEVuZFxceWFudGVjaC15bnAwMS1mcm9udGVuZFxcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkIH0gPSB1c2VBdXRoKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkJylcbiAgICB9IGVsc2Uge1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpXG4gICAgfVxuICB9LCBbaXNBdXRoZW50aWNhdGVkLCByb3V0ZXJdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLXByaW1hcnktNjAwXCI+PC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlQXV0aCIsIkhvbWVQYWdlIiwiaXNBdXRoZW50aWNhdGVkIiwicm91dGVyIiwicHVzaCIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const token = localStorage.getItem('adminToken');\n            if (token) {\n                setIsAuthenticated(true);\n                setUser({\n                    username: 'admin'\n                });\n            }\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (username, password)=>{\n        // Simple auth - replace with real authentication\n        if (username === 'admin' && password === 'admin123') {\n            localStorage.setItem('adminToken', 'dummy-token');\n            setIsAuthenticated(true);\n            setUser({\n                username\n            });\n            return true;\n        }\n        return false;\n    };\n    const logout = ()=>{\n        localStorage.removeItem('adminToken');\n        setIsAuthenticated(false);\n        setUser(null);\n    };\n    const value = {\n        isAuthenticated,\n        user,\n        login,\n        logout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDQ2xvdWQlMjBIZXJvZXMlMjBBZnJpY2ElNUMlNUNZQU5URUNILVlOUDAxLSUyMEZyb250RW5kJTVDJTVDeWFudGVjaC15bnAwMS1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBMEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGJpY2hlXFxcXENsb3VkIEhlcm9lcyBBZnJpY2FcXFxcWUFOVEVDSC1ZTlAwMS0gRnJvbnRFbmRcXFxceWFudGVjaC15bnAwMS1mcm9udGVuZFxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDQ2xvdWQlMjBIZXJvZXMlMjBBZnJpY2ElNUMlNUNZQU5URUNILVlOUDAxLSUyMEZyb250RW5kJTVDJTVDeWFudGVjaC15bnAwMS1mcm9udGVuZCU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDQ2xvdWQlMjBIZXJvZXMlMjBBZnJpY2ElNUMlNUNZQU5URUNILVlOUDAxLSUyMEZyb250RW5kJTVDJTVDeWFudGVjaC15bnAwMS1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNiaWNoZSU1QyU1Q0Nsb3VkJTIwSGVyb2VzJTIwQWZyaWNhJTVDJTVDWUFOVEVDSC1ZTlAwMS0lMjBGcm9udEVuZCU1QyU1Q3lhbnRlY2gteW5wMDEtZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxDbG91ZCBIZXJvZXMgQWZyaWNhXFxcXFlBTlRFQ0gtWU5QMDEtIEZyb250RW5kXFxcXHlhbnRlY2gteW5wMDEtZnJvbnRlbmRcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CCloud%20Heroes%20Africa%5C%5CYANTECH-YNP01-%20FrontEnd%5C%5Cyantech-ynp01-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CCloud%20Heroes%20Africa%5CYANTECH-YNP01-%20FrontEnd%5Cyantech-ynp01-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();