version: '3.8'

services:
  yantech-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yantech-ynp01-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - yantech-network

networks:
  yantech-network:
    driver: bridge

#volumes:
  # Add any volumes here if needed for persistent data
