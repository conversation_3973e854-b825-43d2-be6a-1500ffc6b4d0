import axios from 'axios'
import type { Application, ApplicationFormData, Notification } from '@/types'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

export const getApplications = async (): Promise<Application[]> => {
  try {
    const response = await api.get('/apps')
    return response.data
  } catch (error) {
    throw new Error('Failed to fetch applications')
  }
}

export const createApplication = async (applicationData: ApplicationFormData): Promise<Application> => {
  try {
    const response = await api.post('/app', applicationData)
    return response.data
  } catch (error: any) {
    throw new Error(error.response?.data?.detail || 'Failed to create application')
  }
}

export const updateApplication = async (id: string, applicationData: ApplicationFormData): Promise<Application> => {
  try {
    const response = await api.put(`/app/${id}`, applicationData)
    return response.data
  } catch (error) {
    throw new Error('Failed to update application')
  }
}

export const deleteApplication = async (id: string): Promise<void> => {
  try {
    await api.delete(`/app/${id}`)
  } catch (error) {
    throw new Error('Failed to delete application')
  }
}

export const getNotifications = async (applicationId: string): Promise<Notification[]> => {
  try {
    const response = await api.get(`/app/${applicationId}/notifications`)
    return response.data
  } catch (error) {
    throw new Error('Failed to fetch notifications')
  }
}

