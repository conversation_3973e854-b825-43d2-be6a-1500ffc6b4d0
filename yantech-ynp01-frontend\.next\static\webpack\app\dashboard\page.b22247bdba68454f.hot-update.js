"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApplication: () => (/* binding */ createApplication),\n/* harmony export */   deleteApplication: () => (/* binding */ deleteApplication),\n/* harmony export */   getApplications: () => (/* binding */ getApplications),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   updateApplication: () => (/* binding */ updateApplication)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://18.212.82.100:8001\";\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\nconst getApplications = async ()=>{\n    console.log(\"[getApplications] Sending GET request to /apps\");\n    try {\n        const response = await api.get(\"/apps\");\n        console.log(\"[getApplications] Response received:\", response.status, response.data);\n        return response.data;\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"[getApplications] Error occurred:\", {\n            message: error.message,\n            status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n            data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n        });\n        throw new Error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to fetch applications\");\n    }\n};\nconst createApplication = async (applicationData)=>{\n    console.debug(\"[createApplication] Input data:\", applicationData);\n    try {\n        const response = await api.post(\"/app\", applicationData);\n        console.debug(\"[createApplication] Response status:\", response.status);\n        console.debug(\"[createApplication] Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        var _error_response, _error_response_data, _error_response1;\n        console.error(\"[createApplication] Error:\", error);\n        console.error(\"[createApplication] Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n        throw new Error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to create application -api\");\n    }\n};\nconst updateApplication = async (id, applicationData)=>{\n    try {\n        const response = await api.put(\"/app/\".concat(id), applicationData);\n        return response.data;\n    } catch (error) {\n        var _error_response;\n        console.error(\"[updateApplication] Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n        throw new Error(\"Failed to update application\");\n    }\n};\nconst deleteApplication = async (id)=>{\n    try {\n        await api.delete(\"/app/\".concat(id));\n    } catch (error) {\n        throw new Error(\"Failed to delete application\");\n    }\n};\nconst getNotifications = async (applicationId)=>{\n    try {\n        const response = await api.get(\"/app/\".concat(applicationId, \"/notifications\"));\n        return response.data;\n    } catch (error) {\n        throw new Error(\"Failed to fetch notifications\");\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ })

});