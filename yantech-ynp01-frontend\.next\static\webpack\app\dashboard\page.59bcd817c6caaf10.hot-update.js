"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/ApplicationForm.tsx":
/*!****************************************!*\
  !*** ./components/ApplicationForm.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApplicationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _barrel_optimize_names_RxCross2_react_icons_rx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=RxCross2!=!react-icons/rx */ \"(app-pages-browser)/./node_modules/react-icons/rx/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbLoader2_react_icons_tb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=TbLoader2!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ApplicationForm(param) {\n    let { onClose, onSuccess } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        App_name: '',\n        Application: '',\n        Email: '',\n        Domain: ''\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            await (0,_services_api__WEBPACK_IMPORTED_MODULE_2__.createApplication)(formData);\n            onSuccess();\n        } catch (error) {\n            if (error instanceof Error) {\n                setError(error.message);\n            } else {\n                setError(\"Failed to create application\");\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl w-full max-w-md animate-slide-up\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Register New Application\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RxCross2_react_icons_rx__WEBPACK_IMPORTED_MODULE_3__.RxCross2, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"App_name\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Application Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"App_name\",\n                                    name: \"App_name\",\n                                    type: \"text\",\n                                    value: formData.App_name,\n                                    onChange: handleChange,\n                                    className: \"input-field\",\n                                    placeholder: \"e.g., CHA - Student Platform\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"Application\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Application ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"Application\",\n                                    name: \"Application\",\n                                    type: \"text\",\n                                    value: formData.Application,\n                                    onChange: handleChange,\n                                    className: \"input-field\",\n                                    placeholder: \"e.g., App1\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"Email\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"Email\",\n                                    name: \"Email\",\n                                    type: \"email\",\n                                    value: formData.Email,\n                                    onChange: handleChange,\n                                    className: \"input-field\",\n                                    placeholder: \"e.g., <EMAIL>\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"Domain\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Domain\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"Domain\",\n                                    name: \"Domain\",\n                                    type: \"text\",\n                                    value: formData.Domain,\n                                    onChange: handleChange,\n                                    className: \"input-field\",\n                                    placeholder: \"e.g., cha.com\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-danger-50 border border-danger-200 text-danger-600 px-4 py-3 rounded-lg text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary flex-1 justify-center p-2 rounded\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"btn-primary flex-1 justify-center p-2 rounded\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbLoader2_react_icons_tb__WEBPACK_IMPORTED_MODULE_4__.TbLoader2, {\n                                                className: \"w-4 h-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Creating...\"\n                                        ]\n                                    }, void 0, true) : \"Create Application\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Cloud Heroes Africa\\\\YANTECH-YNP01- FrontEnd\\\\yantech-ynp01-frontend\\\\components\\\\ApplicationForm.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(ApplicationForm, \"tO+4osOay6ywmGpSkYtMi8jwMao=\");\n_c = ApplicationForm;\nvar _c;\n$RefreshReg$(_c, \"ApplicationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ApplicationForm.tsx\n"));

/***/ })

});