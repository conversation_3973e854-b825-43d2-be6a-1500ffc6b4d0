"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApplication: () => (/* binding */ createApplication),\n/* harmony export */   deleteApplication: () => (/* binding */ deleteApplication),\n/* harmony export */   getApplications: () => (/* binding */ getApplications),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   requestNotification: () => (/* binding */ requestNotification),\n/* harmony export */   updateApplication: () => (/* binding */ updateApplication)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Use the Next.js API proxy to avoid CORS issues\nconst API_BASE_URL = \"/api/proxy\";\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\nconst getApplications = async ()=>{\n    console.log(\"[getApplications] Sending GET request to /apps\");\n    try {\n        const response = await api.get(\"/apps\");\n        console.log(\"[getApplications] Response received:\", response.status, response.data);\n        return response.data;\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"[getApplications] Error occurred:\", {\n            message: error.message,\n            status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n            data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n        });\n        throw new Error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to fetch applications\");\n    }\n};\nconst createApplication = async (applicationData)=>{\n    console.debug(\"[createApplication] Input data:\", applicationData);\n    try {\n        const response = await api.post(\"/app\", applicationData);\n        console.debug(\"[createApplication] Response status:\", response.status);\n        console.debug(\"[createApplication] Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        var _error_response, _error_response_data, _error_response1;\n        console.error(\"[createApplication] Error:\", error);\n        console.error(\"[createApplication] Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n        throw new Error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to create application -api\");\n    }\n};\nconst requestNotification = async (Application, Recipient, Subject, Message, OutputType, Interval)=>{};\nconst updateApplication = async (id, applicationData)=>{\n    try {\n        const response = await api.put(\"/app/\".concat(id), applicationData);\n        return response.data;\n    } catch (error) {\n        var _error_response;\n        console.error(\"[updateApplication] Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n        throw new Error(\"Failed to update application\");\n    }\n};\nconst deleteApplication = async (id)=>{\n    try {\n        await api.delete(\"/app/\".concat(id));\n    } catch (error) {\n        throw new Error(\"Failed to delete application\");\n    }\n};\nconst getNotifications = async (applicationId)=>{\n    try {\n        const response = await api.get(\"/app/\".concat(applicationId, \"/notifications\"));\n        return response.data;\n    } catch (error) {\n        throw new Error(\"Failed to fetch notifications\");\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ })

});