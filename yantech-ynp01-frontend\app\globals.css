@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    @apply scroll-smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }

  /* Better focus states for keyboard navigation */
  [type="text"]:focus,
  [type="email"]:focus,
  [type="url"]:focus,
  [type="password"]:focus,
  [type="number"]:focus,
  [type="date"]:focus,
  [type="datetime-local"]:focus,
  [type="month"]:focus,
  [type="search"]:focus,
  [type="tel"]:focus,
  [type="time"]:focus,
  [type="week"]:focus,
  [multiple]:focus,
  textarea:focus,
  select:focus {
    @apply ring-2 ring-primary-500 border-transparent outline-none;
  }
}

@layer components {
  /* Button Base Styles */
  .btn {
    @apply font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center gap-2 
           disabled:opacity-50 disabled:cursor-not-allowed focus-visible:outline-none 
           focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-500;
  }

  /* <PERSON><PERSON> Variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700;
  }
  
  .btn-danger {
    @apply bg-danger-500 hover:bg-danger-600 text-white;
  }

  /* Form Elements */
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-transparent 
           bg-white text-gray-900 placeholder-gray-400;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
  }

  .card-body {
    @apply p-6;
  }

  /* Status Indicators */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-sent {
    @apply bg-success-100 text-success-800;
  }
  
  .status-pending {
    @apply bg-warning-100 text-warning-800;
  }
  
  .status-failed {
    @apply bg-danger-100 text-danger-800;
  }
}

@layer utilities {
  /* Custom utilities if needed */
  .animate-pulse-fast {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}
