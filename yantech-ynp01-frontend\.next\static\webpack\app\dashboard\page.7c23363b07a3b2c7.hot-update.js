"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApplication: () => (/* binding */ createApplication),\n/* harmony export */   deleteApplication: () => (/* binding */ deleteApplication),\n/* harmony export */   getApplications: () => (/* binding */ getApplications),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   updateApplication: () => (/* binding */ updateApplication)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\nconst getApplications = async ()=>{\n    try {\n        const response = await api.get(\"http://localhost:8001/app\");\n        return response.data;\n    } catch (error) {\n        throw new Error('Failed to fetch applications');\n    }\n};\nconst createApplication = async (applicationData)=>{\n    try {\n        const response = await api.post('/app', applicationData);\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || 'Failed to create application');\n    }\n};\nconst updateApplication = async (id, applicationData)=>{\n    try {\n        const response = await api.put(\"/app/\".concat(id), applicationData);\n        return response.data;\n    } catch (error) {\n        throw new Error('Failed to update application');\n    }\n};\nconst deleteApplication = async (id)=>{\n    try {\n        await api.delete(\"/app/\".concat(id));\n    } catch (error) {\n        throw new Error('Failed to delete application');\n    }\n};\nconst getNotifications = async (applicationId)=>{\n    try {\n        const response = await api.get(\"/app/\".concat(applicationId, \"/notifications\"));\n        return response.data;\n    } catch (error) {\n        throw new Error('Failed to fetch notifications');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ })

});